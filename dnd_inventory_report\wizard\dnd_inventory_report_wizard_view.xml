<odoo>
    <record id="dnd_inventory_report_wizard_form" model="ir.ui.view">
        <field name="name">dnd.inventory.report.wizard.form</field>
        <field name="model">dnd.inventory.report.wizard</field>
        <field name="arch" type="xml">
            <form>
                <group name="criteria">
                    <group>
                        <field name="date_from"/>
                        <field name="date_to"/>
                        <field name="is_groupby_location"/>
                        <field name="len_product" invisible="1"/>
                    </group>
                    <group>
                        <field name="location_id" options="{'no_open': True, 'no_create': True}" domain="[('usage','=','internal'),('active','=',True)]"/>
                        <field name="product_ids" widget="many2many_tags" options="{'no_open': True, 'no_create': True}" domain="[('active', '=', True), ('detailed_type', '=', 'product')]"/>
                        <field name="product_category_ids" widget="many2many_tags" options="{'no_open': True, 'no_create': True}"/>
                    </group>
                </group>
                <footer>
                    <button name="button_view" string="View Report" type="object" class="oe_highlight" />
                    <button name="button_view_details" string="View Details" type="object" class="oe_highlight" invisible="len_product != 1"/>
                    <button string="Cancel" class="oe_link" special="cancel" />
                </footer>
            </form>
        </field>
    </record>
    <record id="dnd_inventory_report_action" model="ir.actions.act_window">
        <field name="name">D&amp;D Inventory Report</field>
        <field name="res_model">dnd.inventory.report.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
    <menuitem id="dnd_inventory_report_menu" action="dnd_inventory_report_action" parent="stock.menu_warehouse_report" sequence="30" />
</odoo>
