from odoo import api, fields, models, tools

class DndInventoryDetailsReport(models.Model):
    _name = "dnd.inventory.details.report"
    _description = "Dnd Inventory Details Report"
    _auto = False

    date = fields.Datetime(readonly=True)
    product_id = fields.Many2one(comodel_name="product.product", readonly=True)
    product_qty = fields.Float(readonly=True)
    product_uom = fields.Many2one(comodel_name="uom.uom", readonly=True)
    product_category = fields.Many2one(
        comodel_name="product.category", readonly=True)
    unit_cost = fields.Float(readonly=True)
    reference = fields.Char(readonly=True)
    partner_id = fields.Many2one(comodel_name="res.partner", readonly=True)
    origin = fields.Char(readonly=True)
    location_id = fields.Many2one(comodel_name="stock.location", readonly=True)
    location_dest_id = fields.Many2one(
        comodel_name="stock.location", readonly=True)
    initial = fields.Float(readonly=True)
    initial_amount = fields.Float(readonly=True)
    product_in = fields.Float(readonly=True)
    product_out = fields.Float(readonly=True)
    picking_id = fields.Many2one(comodel_name="stock.picking", readonly=True)

    def view_report_details(self, filters):
        report = self.env["dnd.inventory.report.wizard"].create(filters)
        self.env["dnd.inventory.details.report"].init_results(report)
        details = self.env["dnd.inventory.details.report"].search([])
        data = {
            'product_default_code': report.product_ids.default_code,
            'product_name': report.product_ids.name,
            'date_from': report.date_from or None,
            'date_to': report.date_to or fields.Date.context_today(self),
            'location': report.location_id.complete_name or None,
            'category': report.product_ids.categ_id.complete_name or None,
            'detail_ids': details.ids,
        }
        return self.env.ref('dnd_inventory_report.action_dnd_inventory_details_report_html').with_context(active_model="dnd.inventory.details.report").report_action(details.ids, data=data)

    # ...existing code...
    def _get_locations(self, location_id, is_groupby_location):
        if (location_id):
            if is_groupby_location:
                locations = tuple(self.env["stock.location"].search(
                    [("id", "child_of", location_id.ids)]).ids)
            else:
                locations = tuple(location_id.ids)
        else:
            locations = tuple(self.env["stock.location"].search(
                [("usage", "=", "internal")]).ids)
            if not locations:
                locations = (-1,)
        return locations

    # ...existing code for init_results and view_report_details, replacing imex by dnd...
