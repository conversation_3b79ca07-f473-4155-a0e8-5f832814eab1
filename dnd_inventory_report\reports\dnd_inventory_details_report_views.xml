<odoo>
    <record id="action_dnd_inventory_details_report_html" model="ir.actions.report">
        <field name="name">D&amp;D Inventory Details Report</field>
        <field name="model">dnd.inventory.details.report</field>
        <field name="report_type">qweb-html</field>
        <field name="report_name">dnd_inventory_report.dnd_inventory_details_report_html</field>
        <field name="report_file">dnd_inventory_report.dnd_inventory_details_report_html</field>
    </record>
    <template id="dnd_inventory_details_report_html">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <t t-set="docs" t-value="env['dnd.inventory.details.report'].browse(detail_ids)"/>
                <div class="o_inventory_reports_page">
                    <link href="/dnd_inventory_report/static/src/css/report.css" rel="stylesheet" />
                    <t t-call="dnd_inventory_report.report_dnd_inventory_details_report_base" />
                </div>
            </t>
        </t>
    </template>
    <template id="report_dnd_inventory_details_report_base">
        <div class="page">
            <div class="row">
                <h4 class="mt0" style="text-align: center;">
                    <span>D&amp;D Inventory report - </span>
                    <t t-out="product_default_code" />
                    <span> - </span>
                    <t t-out="product_name" />
                </h4>
            </div>
            <!-- Display filters -->
            <t t-call="dnd_inventory_report.report_dnd_inventory_details_report_filters" />
            <!-- Display Inventory table -->
            <div class="act_as_table data_table mt4" style="width: 100%;">
                <!-- Display header line-->
                <t t-call="dnd_inventory_report.report_dnd_inventory_details_lines_header" />
                <!-- Display initial lines -->
                <t t-if="docs">
                    <t t-set="line_initial" t-value="docs.filtered(lambda l: not l.product_id)"/>
                    <t t-set="lines" t-value="docs.filtered(lambda l: l.product_id)"/>
                    <t t-if="line_initial">
                        <div class="act_as_row lines">
                            <div class="act_as_cell" />
                            <div class="act_as_cell">
                            Initial
                            </div>
                            <div class="act_as_cell" />
                            <div class="act_as_cell" />
                            <div class="act_as_cell" />
                            <div class="act_as_cell" />
                            <div class="act_as_cell" />
                            <div class="act_as_cell" />
                            <div class="act_as_cell right">
                                <t t-out="'{0:,.2f}'.format(line_initial[0].initial)"/>
                            </div>
                            <div class="act_as_cell right">
                                <t t-out="'{0:,.2f}'.format(line_initial[0].initial_amount)"/>
                            </div>
                        </div>
                        <!-- Display each lines -->
                        <t t-set="product_balance" t-value="line_initial[0].initial" />
                        <t t-set="product_amount" t-value="line_initial[0].initial_amount" />
                        <t t-if="lines">
                            <t t-foreach="lines" t-as="product_line">
                                <t t-set="product_balance" t-value="product_balance + product_line.product_in - product_line.product_out" />
                                <t t-set="product_amount" t-value="product_amount + product_line.product_in * product_line.unit_cost - product_line.product_out * product_line.unit_cost" />
                                <t t-call="dnd_inventory_report.report_dnd_inventory_details_lines" />
                            </t>
                        </t>
                    </t>
                </t>
            </div>
            <p style="page-break-before:always;" />
        </div>
    </template>
    <template id="dnd_inventory_report.report_dnd_inventory_details_report_filters">
        <div class="act_as_table data_table" style="width: 100%;">
            <div class="act_as_row labels">
                <div class="act_as_cell">Date From</div>
                <div class="act_as_cell">Date To</div>
                <div class="act_as_cell">Location</div>
                <div class="act_as_cell">Category</div>
            </div>
            <div class="act_as_row">
                <div class="act_as_cell">
                    <span t-out="date_from" />
                </div>
                <div class="act_as_cell">
                    <span t-out="date_to" />
                </div>
                <div class="act_as_cell">
                    <span t-if="location">
                        <t t-out="location"/>
                    </span>
                    <span t-else="">All stocks</span>
                </div>
                <div class="act_as_cell">
                    <span t-if="category" t-out="category"/>
                </div>
            </div>
        </div>
    </template>

    <template id="dnd_inventory_report.report_dnd_inventory_details_lines_header">
        <div class="act_as_thead">
            <div class="act_as_row labels">
                <div class="act_as_cell">Date</div>
                <div class="act_as_cell">Reference</div>
                <div class="act_as_cell">Partner</div>
                <div class="act_as_cell">Source Location</div>
                <div class="act_as_cell">Dest Location</div>
                <div class="act_as_cell">Price</div>
                <div class="act_as_cell">In</div>
                <div class="act_as_cell">Out</div>
                <div class="act_as_cell">Balance</div>
                <div class="act_as_cell">Amount</div>
            </div>
        </div>
    </template>

    <template id="dnd_inventory_report.report_dnd_inventory_details_lines">
        <div class="act_as_row lines">
            <div class="act_as_cell left">
                <t t-out="product_line.date.strftime('%Y-%m-%d')" />
            </div>
            <div class="act_as_cell left">
                <t t-out="product_line.display_name" />
            </div>
            <div class="act_as_cell left">
                <t t-out="product_line.picking_id.partner_id.name" />
            </div>
            <div class="act_as_cell left">
                <t t-out="product_line.location_id.complete_name" />
            </div>
            <div class="act_as_cell left">
                <t t-out="product_line.location_dest_id.complete_name" />
            </div>
            <div class="act_as_cell right">
                <t t-out="'{0:,.2f}'.format(product_line.unit_cost)" />
            </div>
            <div class="act_as_cell right">
                <t t-out="'{0:,.2f}'.format(product_line.product_in)" />
            </div>
            <div class="act_as_cell right">
                <t t-out="'{0:,.2f}'.format(product_line.product_out)" />
            </div>
            <div class="act_as_cell right">
                <t t-out="'{0:,.2f}'.format(product_balance)" />
            </div>
            <div class="act_as_cell right">
                <t t-out="'{0:,.2f}'.format(product_amount)" />
            </div>
        </div>
    </template>
</odoo>
