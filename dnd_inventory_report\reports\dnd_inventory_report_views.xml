<odoo>
    <record id="view_dnd_inventory_report_search" model="ir.ui.view">
        <field name="name">dnd.inventory.report.search</field>
        <field name="model">dnd.inventory.report</field>
        <field name="arch" type="xml">
            <search string="D&amp;D Inventory Report Search">
                <field name="product_id" string="Product"/>
                <field name="product_category" string="Category"/>
                <field name="location" string="Location"/>
                <separator/>
                <filter name="balance" string="In stock" domain="[('balance','!=',0)]"/>
                <filter name="not_balance" string="Out of stock" domain="[('balance','=',0)]"/>
                <separator/>
                <filter name="dnd" string="D&amp;D Qty" domain="['|',('product_in','!=',0),('product_out','!=',0)]"/>
                <filter name="not_dnd" string="Not D&amp;D Qty" domain="[('product_in','=',0),('product_out','=',0)]"/>
                <group expand="0" string="Group By">
                    <filter string="Product" name="product_id" domain="[]" context="{'group_by': 'product_id'}"/>
                    <filter string="Category" name="product_category" domain="[]" context="{'group_by': 'product_category'}"/>
                    <filter string="Location" name="location" domain="[]" context="{'group_by': 'location'}"/>
                </group>
            </search>
        </field>
    </record>
    <record id="view_dnd_inventory_report_tree" model="ir.ui.view">
        <field name="name">dnd.inventory.report.tree</field>
        <field name="model">dnd.inventory.report</field>
        <field name="arch" type="xml">
            <tree create="false" string="D&amp;D Inventory Report">
                <field name="product_id" string="Product"/>
                <field name="product_uom" string="UoM" optional="show"/>
                <field name="product_category" string="Category" optional="show"/>
                <field name="location" string="Location" optional="show"/>
                <field name="initial" string="Initial" optional="show"/>
                <field name="initial_amount" string="Initial Amount" optional="show"/>
                <field name="product_in" string="Total Qty In" optional="show"/>
                <field name="product_in_amount" string="Total Amount In" optional="show"/>
                <field name="product_out" string="Total Qty Out" optional="show"/>
                <field name="product_out_amount" string="Total Amount Out" optional="show"/>
                <field name="balance" string="Balance" optional="show"/>
                <field name="amount" string="Amount" optional="show"/>
                <button name="report_details" type="object" title="search" class="fa fa-search"/>
            </tree>
        </field>
    </record>
    <record id="action_dnd_inventory_report_tree_view" model="ir.actions.act_window">
        <field name="name">D&amp;D Inventory Report</field>
        <field name="res_model">dnd.inventory.report</field>
        <field name="search_view_id" ref="view_dnd_inventory_report_search" />
        <field name="view_mode">tree</field>
    </record>
    <record id="action_dnd_inventory_report_pdf" model="ir.actions.report">
        <field name="name">Print D&amp;D inventory report</field>
        <field name="model">dnd.inventory.report</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">dnd_inventory_report.dnd_inventory_report_pdf</field>
        <field name="report_file">dnd_inventory_report.dnd_inventory_report_pdf</field>
        <field name="print_report_name">'D&amp;D Inventory Report'</field>
        <field name="binding_model_id" ref="model_dnd_inventory_report"/>
        <field name="binding_type">report</field>
    </record>
    <!-- PDF template and table structure can be adapted similarly -->
</odoo>
